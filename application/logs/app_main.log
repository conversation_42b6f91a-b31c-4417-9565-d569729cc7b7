{"timestamp": "2025-08-30T16:52:30.917589", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:52:30.959199", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:52:31.116202", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:56:40.648950", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "be81f0a6-718f-451f-8285-32769716ecf7", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:56:41.000080", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:56:41.042364", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:56:41.167364", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:46.266807", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:46.624509", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:46.665788", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:46.788820", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:02:52.066483", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "eb661818-c479-49a6-a8f4-8f5cf8e1c054", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:02:52.377941", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:02:52.418971", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:02:52.533421", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:03:02.353690", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "cf5b49bb-82eb-45c0-8421-a0bae41affa0", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:03:02.699374", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:03:02.742257", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:03:02.868124", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:36.422807", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "01a71136-af66-49c4-ae0d-d5518a55429e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:36.769330", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:36.810933", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:36.917971", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:45.593414", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "e14cc5e5-3165-40b6-8a8a-7cc7b07f3c81", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:45.938169", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:45.981332", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:46.085360", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:47.030887", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "e07eeb9a-f964-483f-9d30-29eb66687b5b", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:47.384915", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:47.425789", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:47.529298", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:52.483005", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "3ec8235e-e34a-419d-93a7-322cdc56f178", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:52.798459", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:52.839762", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:52.946525", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:06:55.621850", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "a5182e61-58e0-4030-88a9-2a7745a8cf65", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:06:55.937279", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:06:55.978226", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:06:56.150087", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:07:21.898315", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "1f6092f6-a06e-4929-a833-87846b640a15", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:07:22.198085", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:07:22.239492", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:07:22.337167", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:09:34.643277", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "4dc922c2-9b4c-4205-a8ec-87dd942eeab3", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:09:35.005665", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:09:35.047096", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:09:35.150694", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:12:47.273866", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "13a686bb-06df-489f-8ea1-3ce42b76d13f", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:12:47.595637", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:12:47.636907", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:12:47.736748", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:26.084584", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "7e30e2b1-2e54-43f4-8cc2-b00c3a16490c", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:26.408410", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:26.449459", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:26.548638", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:32.120680", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "1a1fc172-c076-4530-b477-cc72996e84ac", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:32.409435", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:32.450264", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:32.563114", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:17.553472", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "c5fa598a-ce57-4b08-b003-2a6a28f53e71", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:17.878355", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:17.919123", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:18.017910", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:23.895262", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "7526144e-8d88-46f9-b2fc-6b0f83bca566", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:24.219116", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:24.260246", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:59.327064", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:59.368731", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:59.496324", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:18:36.712596", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 44, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "26f3201c-5503-4168-97d9-1879109bf803", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:18:37.084513", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 31, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:18:37.126355", "level": "INFO", "logger": "app.main", "module": "main", "function": "<module>", "line_number": 68, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:18:37.234105", "level": "INFO", "logger": "app.main", "module": "main", "function": "lifespan", "line_number": 42, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
