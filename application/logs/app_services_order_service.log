{"timestamp": "2025-08-30T16:52:59.254958", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 47, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "55a77134-359f-4d99-9ebf-6664907b35c8", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:52:59.255149", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 60, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "55a77134-359f-4d99-9ebf-6664907b35c8", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:52:59.268215", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 115, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "55a77134-359f-4d99-9ebf-6664907b35c8", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:52:59.271958", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 185, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "55a77134-359f-4d99-9ebf-6664907b35c8", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:52:59.301166", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "update_order_status", "line_number": 287, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "55a77134-359f-4d99-9ebf-6664907b35c8", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:53:03.047741", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 47, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "6947b1ae-30da-4f32-b626-f7731edea316", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:53:03.048028", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 60, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "6947b1ae-30da-4f32-b626-f7731edea316", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:53:03.052185", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 115, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "6947b1ae-30da-4f32-b626-f7731edea316", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:53:03.055989", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 185, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "6947b1ae-30da-4f32-b626-f7731edea316", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:53:03.082545", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "update_order_status", "line_number": 287, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "6947b1ae-30da-4f32-b626-f7731edea316", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:38.674438", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 47, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:38.674836", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 60, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:38.678775", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 115, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:38.682558", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "create_order", "line_number": 185, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:38.722779", "level": "INFO", "logger": "app.services.order_service", "module": "order_service", "function": "update_order_status", "line_number": 287, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
