{"timestamp": "2025-08-30T16:52:31.101637", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:56:41.156912", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:46.778774", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:02:52.522180", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:03:02.857259", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:36.908702", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:46.077047", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:47.520451", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:52.936917", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:06:56.137790", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:07:22.328664", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:09:35.140685", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:12:47.728006", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:26.539900", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:32.552854", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:18.009423", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:59.478146", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:18:37.225390", "level": "INFO", "logger": "razorpay_service", "module": "razorpay_service", "function": "__init__", "line_number": 40, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
