{"timestamp": "2025-08-30T16:52:31.033819", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:56:41.106330", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:46.723794", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:02:52.472502", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:03:02.806635", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:36.864054", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:46.035512", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:47.475717", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:52.891280", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:06:56.054406", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:07:22.287511", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:09:35.097302", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:12:47.686571", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:26.497626", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:32.503200", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:17.968271", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:59.422249", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:18:37.180517", "level": "ERROR", "logger": "redis_wrapper", "module": "redis_wrapper", "function": "__init__", "line_number": 46, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
