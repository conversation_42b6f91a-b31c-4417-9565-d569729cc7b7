{"timestamp": "2025-08-30T16:52:59.284544", "level": "INFO", "logger": "app.payment_service", "module": "payment_service", "function": "create_payment_record", "line_number": 84, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "55a77134-359f-4d99-9ebf-6664907b35c8", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:53:03.060712", "level": "INFO", "logger": "app.payment_service", "module": "payment_service", "function": "create_payment_record", "line_number": 84, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "6947b1ae-30da-4f32-b626-f7731edea316", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:38.703982", "level": "INFO", "logger": "app.payment_service", "module": "payment_service", "function": "create_payment_record", "line_number": 84, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
