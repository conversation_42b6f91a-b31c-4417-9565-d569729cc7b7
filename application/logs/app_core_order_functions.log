{"timestamp": "2025-08-30T16:52:59.096136", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_order_core", "line_number": 30, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "55a77134-359f-4d99-9ebf-6664907b35c8", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:52:59.273469", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_payment_records_for_order", "line_number": 279, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "55a77134-359f-4d99-9ebf-6664907b35c8", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:52:59.284756", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_payment_records_for_order", "line_number": 342, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "55a77134-359f-4d99-9ebf-6664907b35c8", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:52:59.284846", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_order_core", "line_number": 104, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "55a77134-359f-4d99-9ebf-6664907b35c8", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:53:02.914531", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_order_core", "line_number": 30, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "6947b1ae-30da-4f32-b626-f7731edea316", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:53:03.056347", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_payment_records_for_order", "line_number": 279, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "6947b1ae-30da-4f32-b626-f7731edea316", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:53:03.060972", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_payment_records_for_order", "line_number": 342, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "6947b1ae-30da-4f32-b626-f7731edea316", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:53:03.061121", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_order_core", "line_number": 104, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "6947b1ae-30da-4f32-b626-f7731edea316", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:55:24.703390", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_order_core", "line_number": 30, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "3c4cde3c-faf9-4193-869c-4184f31aa645", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:57:01.052805", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_order_core", "line_number": 30, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "08713611-a46a-4b20-ac84-4633c3e24aa0", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:38.507775", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_order_core", "line_number": 30, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:38.683924", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_payment_records_for_order", "line_number": 280, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:38.704202", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_payment_records_for_order", "line_number": 343, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:38.704330", "level": "INFO", "logger": "app.core.order_functions", "module": "order_functions", "function": "create_order_core", "line_number": 104, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": "393e5ca6-073f-4146-b554-7cd7ef23cf5e", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/health", "size_in_bytes": 0, "status_code": 0, "version": ""}
