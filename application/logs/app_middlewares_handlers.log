{"timestamp": "2025-08-30T16:55:24.873493", "level": "ERROR", "logger": "app.middlewares.handlers", "module": "handlers", "function": "_http_exception_handler", "line_number": 83, "environment": "UAT", "service": "rozana-oms", "exception": "400: User ID and Customer ID do not match", "user_id": null, "request_id": "3c4cde3c-faf9-4193-869c-4184f31aa645", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:57:01.222118", "level": "ERROR", "logger": "app.middlewares.handlers", "module": "handlers", "function": "_http_exception_handler", "line_number": 83, "environment": "UAT", "service": "rozana-oms", "exception": "400: User ID and Customer ID do not match", "user_id": null, "request_id": "08713611-a46a-4b20-ac84-4633c3e24aa0", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "POST", "request_path": "/app/v1/create_order", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:06.787023", "level": "ERROR", "logger": "app.middlewares.handlers", "module": "handlers", "function": "_http_exception_handler", "line_number": 83, "environment": "UAT", "service": "rozana-oms", "exception": "404: Order not found", "user_id": null, "request_id": "afd01b0d-aac7-418d-a611-6e5bd25a0f5f", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/app/v1/order_details", "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:16:11.949534", "level": "ERROR", "logger": "app.middlewares.handlers", "module": "handlers", "function": "_validation_exception_handler", "line_number": 22, "environment": "UAT", "service": "rozana-oms", "exception": "[{'type': 'missing', 'loc': ('query', 'facility_name'), 'msg': 'Field required', 'input': None}]", "user_id": null, "request_id": "03beb053-ab50-4b81-a368-ede615405880", "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": "GET", "request_path": "/pos/v1/order_details", "size_in_bytes": 0, "status_code": 0, "version": ""}
