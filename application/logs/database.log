{"timestamp": "2025-08-30T16:52:30.397149", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:52:30.868567", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:56:40.959820", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T16:58:46.585530", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:02:52.338840", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:03:02.663123", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:36.728373", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:04:45.897810", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:47.345711", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:05:52.764148", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:06:55.898250", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:07:22.158192", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:09:34.969248", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:12:47.557084", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:26.372392", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:13:32.370036", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:17.842989", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:24.185242", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:14:59.285740", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
{"timestamp": "2025-08-30T17:18:37.045192", "level": "INFO", "logger": "database", "module": "database", "function": "<module>", "line_number": 116, "environment": "UAT", "service": "rozana-oms", "user_id": null, "request_id": null, "duration": 0.0, "header_referer": "", "hostname": "", "app_name": "", "module_name": "", "request": "", "response": "", "request_method": null, "request_path": null, "size_in_bytes": 0, "status_code": 0, "version": ""}
